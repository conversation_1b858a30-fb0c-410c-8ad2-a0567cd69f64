#!/usr/bin/env python3
"""
PlexMovieAutomator/_internal/utils/ocr_utils.py

Subtitle Edit Integration for OCR Processing
Replaces complex custom OCR pipeline with Subtitle Edit's built-in OCR capabilities.
"""

import logging
import subprocess
import tempfile
import shutil
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import asyncio
import os

logger = logging.getLogger(__name__)

async def convert_sup_to_srt_with_subtitle_edit(
    sup_file: Path,
    output_srt: Path,
    settings: dict
) -> bool:
    """
    Convert SUP file to SRT using Subtitle Edit with Ollama Vision OCR.

    This replaces the complex BDSup2Sub + NGC Models pipeline with a simple
    Subtitle Edit call that handles everything internally.

    Args:
        sup_file: Path to the SUP file to convert
        output_srt: Path where the SRT file should be saved
        settings: Settings dictionary containing Subtitle Edit configuration

    Returns:
        bool: True if conversion was successful, False otherwise
    """
    try:
        logger.info(f"🎬 Converting SUP to SRT using Subtitle Edit with Ollama Vision")
        logger.info(f"   Input: {sup_file}")
        logger.info(f"   Output: {output_srt}")

        # Get Subtitle Edit configuration from settings
        subtitle_edit_path = settings.get('Executables', {}).get('subtitle_edit_path', 'SubtitleEdit.exe')
        ocr_method = settings.get('SubtitleHandler', {}).get('ocr_method', 'ollama_vision')
        ollama_model = settings.get('SubtitleHandler', {}).get('ollama_model', 'qwen2.5vl:latest')
        ocr_language = settings.get('SubtitleHandler', {}).get('ocr_language', 'English')

        logger.info(f"   OCR Method: {ocr_method}")
        logger.info(f"   Ollama Model: {ollama_model}")
        logger.info(f"   Language: {ocr_language}")

        # Check if Subtitle Edit executable exists
        if not Path(subtitle_edit_path).exists():
            logger.error(f"Subtitle Edit not found at: {subtitle_edit_path}")
            logger.error("Please install Subtitle Edit or update the path in settings.ini")
            return False

        # Check if SUP file exists
        if not sup_file.exists():
            logger.error(f"SUP file not found: {sup_file}")
            return False

        # Create output directory if it doesn't exist
        output_srt.parent.mkdir(parents=True, exist_ok=True)

        # Use direct Ollama Vision OCR - fully automated!
        logger.info("🚀 Starting direct Ollama Vision OCR (fully automated)...")
        logger.info(f"   File: {sup_file.name}")
        logger.info(f"   Method: {ocr_method}")
        logger.info(f"   Model: {ollama_model}")
        logger.info(f"   Language: {ocr_language}")

        try:
            # Use BDSup2Sub + Ollama Vision for fully automated OCR
            logger.info("🚀 Using BDSup2Sub + Ollama Vision (fully automated)...")

            success = await _convert_sup_with_bdsup2sub_ollama(
                sup_file, output_srt, ollama_model, ocr_language
            )

            if success:
                logger.info(f"✅ BDSup2Sub + Ollama Vision OCR successful: {output_srt}")
                return True
            else:
                logger.error("❌ BDSup2Sub + Ollama Vision OCR failed")
                logger.info("🔄 Falling back to semi-automatic Subtitle Edit...")
                return await _fallback_subtitle_edit_manual(
                    subtitle_edit_path, sup_file, output_srt
                )

        except Exception as e:
            logger.error(f"❌ Failed BDSup2Sub approach: {e}")
            logger.info("🔄 Falling back to semi-automatic Subtitle Edit...")
            return await _fallback_subtitle_edit_manual(
                subtitle_edit_path, sup_file, output_srt
            )

    except Exception as e:
        logger.error(f"❌ Error in Subtitle Edit conversion: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

async def _convert_sup_with_ollama_vision(
    sup_file: Path,
    output_srt: Path,
    ollama_model: str,
    ocr_language: str
) -> bool:
    """
    Convert SUP to SRT using direct Ollama Vision OCR.
    This bypasses Subtitle Edit UI automation entirely.
    """
    try:
        logger.info("🤖 Starting direct Ollama Vision OCR processing...")

        # First, extract images from SUP file
        logger.info("📦 Extracting subtitle images from SUP file...")
        subtitle_images = await _extract_images_from_sup(sup_file)

        if not subtitle_images:
            logger.error("❌ No subtitle images found in SUP file")
            return False

        logger.info(f"✅ Extracted {len(subtitle_images)} subtitle images")

        # Process each image with Ollama Vision
        logger.info("🔍 Processing images with Ollama Vision...")
        srt_entries = []

        for i, (start_time, end_time, image_data) in enumerate(subtitle_images):
            logger.info(f"   Processing image {i+1}/{len(subtitle_images)}...")

            # OCR the image using Ollama Vision
            text = await _ocr_image_with_ollama(image_data, ollama_model, ocr_language)

            if text and text.strip():
                # Create SRT entry
                srt_entries.append({
                    'index': len(srt_entries) + 1,
                    'start': start_time,
                    'end': end_time,
                    'text': text.strip()
                })
                logger.debug(f"   OCR result: {text.strip()}")
            else:
                logger.debug(f"   No text detected in image {i+1}")

        # Generate SRT file
        logger.info("📝 Generating SRT file...")
        success = await _generate_srt_file(srt_entries, output_srt)

        if success:
            logger.info(f"✅ Direct Ollama Vision OCR completed: {len(srt_entries)} subtitles")
            return True
        else:
            logger.error("❌ Failed to generate SRT file")
            return False

    except Exception as e:
        logger.error(f"❌ Direct Ollama Vision OCR failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


async def _fallback_subtitle_edit_manual(
    subtitle_edit_path: str,
    sup_file: Path,
    output_srt: Path
) -> bool:
    """
    Fallback to semi-automatic Subtitle Edit approach.
    """
    try:
        logger.info("🔄 Opening Subtitle Edit for manual OCR...")

        # Open Subtitle Edit with the SUP file
        import subprocess
        cmd = [str(subtitle_edit_path), str(sup_file)]
        logger.info(f"   Opening: {' '.join(cmd)}")

        # Start Subtitle Edit
        process = subprocess.Popen(
            cmd,
            cwd=str(sup_file.parent)
        )

        logger.info("✅ Subtitle Edit opened successfully!")
        logger.info("🖱️  Please click the 'Start OCR' button in Subtitle Edit")
        logger.info("⏳ Waiting for OCR to complete...")

        # Wait for the user to start OCR and for it to complete
        max_wait_time = 1800  # 30 minutes max
        check_interval = 10   # Check every 10 seconds
        elapsed_time = 0

        while elapsed_time < max_wait_time:
            await asyncio.sleep(check_interval)
            elapsed_time += check_interval

            # Check if output file was created
            if output_srt.exists() and output_srt.stat().st_size > 0:
                logger.info(f"✅ Manual OCR completed successfully: {output_srt}")
                return True

            # Log progress every minute
            if elapsed_time % 60 == 0:
                minutes = elapsed_time // 60
                logger.info(f"   Still waiting for OCR completion... ({minutes} minutes elapsed)")

        # Timeout reached
        logger.error("❌ Timeout waiting for OCR completion")
        return False

    except Exception as e:
        logger.error(f"❌ Failed to open Subtitle Edit: {e}")
        return False


async def _convert_sup_with_bdsup2sub_ollama(
    sup_file: Path,
    output_srt: Path,
    ollama_model: str,
    ocr_language: str,
    max_images: int = None
) -> bool:
    """
    Convert SUP to SRT using BDSup2Sub for image extraction + Ollama Vision for OCR.
    This is the most reliable approach combining proven SUP parsing with accurate OCR.
    """
    try:
        logger.info("🤖 Starting BDSup2Sub + Ollama Vision OCR processing...")

        # Step 1: Use BDSup2Sub to extract images from SUP
        logger.info("📦 Extracting subtitle images using BDSup2Sub...")
        subtitle_images, extraction_folder = await _extract_images_with_bdsup2sub(sup_file)

        if not subtitle_images or not extraction_folder:
            logger.error("❌ No subtitle images extracted by BDSup2Sub")
            return False

        logger.info(f"✅ BDSup2Sub extracted {len(subtitle_images)} subtitle images")

        # Step 2: Process each image with Ollama Vision
        # Limit images for testing if specified
        images_to_process = subtitle_images
        if max_images and max_images > 0:
            images_to_process = subtitle_images[:max_images]
            logger.info(f"🔍 Processing first {len(images_to_process)} images with Ollama Vision (limited for testing)...")
        else:
            logger.info(f"🔍 Processing all {len(images_to_process)} images with Ollama Vision...")

        srt_entries = []

        for i, (start_time, end_time, image_path) in enumerate(images_to_process):
            logger.info(f"   Processing image {i+1}/{len(images_to_process)}...")

            # Read image file
            with open(image_path, 'rb') as f:
                image_data = f.read()

            # OCR the image using Ollama Vision
            text = await _ocr_image_with_ollama(image_data, ollama_model, ocr_language)

            if text and text.strip():
                # Create SRT entry
                srt_entries.append({
                    'index': len(srt_entries) + 1,
                    'start': start_time,
                    'end': end_time,
                    'text': text.strip()
                })
                logger.info(f"   ✅ OCR result: {text.strip()}")
            else:
                logger.debug(f"   No text detected in image {i+1}")

        # Step 3: Generate SRT file
        logger.info("📝 Generating SRT file...")
        success = await _generate_srt_file(srt_entries, output_srt)

        if success:
            logger.info(f"✅ BDSup2Sub + Ollama Vision OCR completed: {len(srt_entries)} subtitles")

            # Clean up the entire extraction folder (images and XML)
            try:
                if extraction_folder and extraction_folder.exists():
                    import shutil
                    shutil.rmtree(extraction_folder)
                    logger.info(f"🧹 Cleaned up extraction folder: {extraction_folder}")
            except Exception as e:
                logger.warning(f"Failed to clean up extraction folder: {e}")

            return True
        else:
            logger.error("❌ Failed to generate SRT file")

            # Clean up extraction folder even on failure
            try:
                if extraction_folder and extraction_folder.exists():
                    import shutil
                    shutil.rmtree(extraction_folder)
                    logger.info(f"🧹 Cleaned up extraction folder after failure: {extraction_folder}")
            except Exception as e:
                logger.warning(f"Failed to clean up extraction folder after failure: {e}")

            return False

    except Exception as e:
        logger.error(f"❌ BDSup2Sub + Ollama Vision OCR failed: {e}")
        import traceback
        logger.error(traceback.format_exc())

        # Clean up extraction folder on exception
        try:
            if 'extraction_folder' in locals() and extraction_folder and extraction_folder.exists():
                import shutil
                shutil.rmtree(extraction_folder)
                logger.info(f"🧹 Cleaned up extraction folder after exception: {extraction_folder}")
        except Exception as cleanup_error:
            logger.warning(f"Failed to clean up extraction folder after exception: {cleanup_error}")

        return False


async def _extract_images_with_bdsup2sub(sup_file: Path) -> tuple[list, Path]:
    """
    Use BDSup2Sub to extract subtitle images from SUP file.
    Returns tuple of (subtitle_images_list, extraction_folder_path).
    The extraction folder contains images and XML and should be cleaned up after processing.
    """
    try:
        import subprocess
        import xml.etree.ElementTree as ET
        from pathlib import Path

        logger.info("🔧 Using BDSup2Sub to extract subtitle images...")

        # Create extraction directory within the movie's _Processed_Subtitles folder
        sup_parent = sup_file.parent  # This should be the _Processed_Subtitles folder
        extraction_folder = sup_parent / "bd2sup_extraction"

        # Create the extraction folder
        extraction_folder.mkdir(exist_ok=True)
        logger.info(f"📁 Created extraction folder: {extraction_folder}")

        temp_path = extraction_folder

        # Check if BDSup2Sub is available - look in our tools directory first
        script_dir = Path(__file__).parent.parent  # Go up from utils to _internal
        local_bdsup2sub = script_dir / "tools" / "bdsup2sub.jar"

        bdsup2sub_paths = [
            str(local_bdsup2sub),  # Our local copy first
            "BDSup2Sub.jar",
            "C:/Program Files/BDSup2Sub/BDSup2Sub.jar",
            "C:/Program Files (x86)/BDSup2Sub/BDSup2Sub.jar",
            str(Path.home() / "BDSup2Sub.jar"),
            "bdsup2sub"  # If it's in PATH
        ]

        bdsup2sub_path = None
        for path in bdsup2sub_paths:
            if Path(path).exists() or path == "bdsup2sub":
                bdsup2sub_path = path
                logger.info(f"✅ Found BDSup2Sub at: {path}")
                break

        if not bdsup2sub_path:
            logger.error("❌ BDSup2Sub not found")
            logger.info("💡 Please install BDSup2Sub:")
            logger.info("   Download from: https://github.com/mjuhasz/BDSup2Sub")
            logger.info("   Or install via: java -jar BDSup2Sub.jar")
            logger.info(f"   Expected location: {local_bdsup2sub}")
            return [], None

        # Prepare BDSup2Sub command
        # BDSup2Sub outputs to XML format with PNG images
        output_xml = temp_path / "subtitle.xml"

        if bdsup2sub_path.endswith(".jar"):
            # Java JAR version - correct BDSup2Sub syntax
            # Use relative paths to avoid issues with spaces in paths
            cmd = [
                "java", "-jar", str(Path(bdsup2sub_path).resolve()),
                "-o", str(output_xml.resolve()),  # Output XML file (absolute path)
                str(Path(sup_file).resolve())  # Input SUP file (absolute path)
            ]
        else:
            # Command line version
            cmd = [
                bdsup2sub_path,
                "-o", str(output_xml.resolve()),
                str(Path(sup_file).resolve())
            ]

        logger.info(f"🚀 Running BDSup2Sub: {' '.join(cmd)}")

        # Run BDSup2Sub
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=str(temp_path)  # Ensure string path
        )

        if result.returncode != 0:
            logger.error(f"❌ BDSup2Sub failed with return code {result.returncode}")
            logger.error(f"   Command: {' '.join(cmd)}")
            logger.error(f"   Stderr: {result.stderr}")
            logger.error(f"   Stdout: {result.stdout}")
            return [], None

        logger.info(f"✅ BDSup2Sub completed successfully")

        # Extract precise timing from BDSup2Sub stdout
        precise_timings = {}
        if result.stdout:
            logger.debug(f"BDSup2Sub output: {result.stdout}")
            # Parse precise timing from stdout like "#> 1 (00:00:52.470)"
            import re
            timing_pattern = r'#> (\d+) \((\d{2}:\d{2}:\d{2}\.\d{3})\)'
            for match in re.finditer(timing_pattern, result.stdout):
                index = int(match.group(1))
                time_str = match.group(2)
                # Convert HH:MM:SS.mmm to seconds
                precise_seconds = _parse_precise_time_to_seconds(time_str)
                precise_timings[index] = precise_seconds
                logger.debug(f"Precise timing #{index}: {time_str} = {precise_seconds:.3f}s")

            logger.info(f"📊 Extracted {len(precise_timings)} precise start timings from BDSup2Sub output")

        if result.stderr:
            logger.debug(f"BDSup2Sub stderr: {result.stderr}")

        logger.info("✅ BDSup2Sub extraction completed")

        # Parse the extracted files using BDSup2Sub XML format
        subtitle_images = []

        # Look for the XML file and PNG files
        xml_files = list(temp_path.glob("*.xml"))

        if not xml_files:
            logger.error("❌ No XML file generated by BDSup2Sub")
            return [], None

            xml_file = xml_files[0]
            logger.info(f"📁 Processing BDSup2Sub XML: {xml_file}")

            try:
                # Parse XML using the same method as the backup script
                import re

                # Read raw XML text and fix any encoding issues
                with open(xml_file, 'r', encoding='utf-8', errors='ignore') as f:
                    xml_data = f.read()

                # Escape any '&' not part of a valid entity
                xml_data = re.sub(r'&(?!amp;|lt;|gt;|quot;|apos;)', '&amp;', xml_data)

                # Parse corrected XML
                tree = ET.ElementTree(ET.fromstring(xml_data))
                root = tree.getroot()

                # Extract frame rate from XML for precise timing
                frame_rate = 25.0  # Default fallback
                try:
                    format_elem = root.find('.//Format')
                    if format_elem is not None:
                        frame_rate_str = format_elem.get('FrameRate', '25')
                        frame_rate = float(frame_rate_str)
                        logger.info(f"📊 Detected frame rate: {frame_rate} fps")
                except Exception as e:
                    logger.debug(f"Could not extract frame rate, using default: {e}")

                # Extract timestamps and graphic filenames
                subtitle_index = 1  # BDSup2Sub uses 1-based indexing
                for event in root.findall('.//Event'):
                    start_tc = event.get('InTC')  # InTC = In Time Code
                    end_tc = event.get('OutTC')   # OutTC = Out Time Code
                    graphic_elem = event.find('Graphic')

                    if start_tc and end_tc and graphic_elem is not None and graphic_elem.text:
                        # Use precise timing from BDSup2Sub stdout if available
                        if subtitle_index in precise_timings:
                            start_seconds = precise_timings[subtitle_index]
                            # For end time, we still need to use frame-based calculation
                            # since BDSup2Sub only outputs start times in its debug output
                            end_seconds = _parse_time_to_seconds(end_tc, frame_rate)
                            logger.debug(f"Using precise start time for subtitle {subtitle_index}: {start_seconds:.3f}s")
                        else:
                            # Fallback to frame-based timing
                            start_seconds = _parse_time_to_seconds(start_tc, frame_rate)
                            end_seconds = _parse_time_to_seconds(end_tc, frame_rate)

                        # Get the PNG filename
                        png_filename = graphic_elem.text
                        png_path = temp_path / png_filename

                        if png_path.exists():
                            # Keep images in the extraction folder (no need to copy)
                            subtitle_images.append((start_seconds, end_seconds, png_path))
                            logger.debug(f"Added subtitle {subtitle_index}: {start_tc} -> {end_tc} ({png_filename})")
                        else:
                            logger.warning(f"PNG file not found: {png_path}")

                        subtitle_index += 1

            except Exception as e:
                logger.error(f"Failed to parse BDSup2Sub XML: {e}")
                import traceback
                logger.error(traceback.format_exc())
                return [], None

        logger.info(f"✅ Prepared {len(subtitle_images)} subtitle images for OCR")
        return subtitle_images, extraction_folder

    except Exception as e:
        logger.error(f"❌ BDSup2Sub extraction failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return [], None


def _parse_precise_time_to_seconds(time_str: str) -> float:
    """
    Parse precise time string (HH:MM:SS.mmm) to seconds.
    Used for BDSup2Sub's precise timing output.
    """
    try:
        # Format: "00:00:52.470"
        parts = time_str.split(':')
        if len(parts) != 3:
            logger.debug(f"Unexpected precise time format: {time_str}")
            return 0.0

        hours = int(parts[0])
        minutes = int(parts[1])
        seconds_parts = parts[2].split('.')
        seconds = int(seconds_parts[0])
        milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0

        total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
        return total_seconds
    except Exception as e:
        logger.debug(f"Failed to parse precise time '{time_str}': {e}")
        return 0.0


def _parse_time_to_seconds(time_str: str, frame_rate: float = 25.0) -> float:
    """
    Parse time string to seconds with precise frame rate.
    BDSup2Sub uses frame-based format like "00:01:23:12" (HH:MM:SS:FF)
    where FF is frame number. Uses actual frame rate from XML for precision.
    """
    try:
        parts = time_str.split(':')
        if len(parts) != 4:
            logger.debug(f"Unexpected time format: {time_str}")
            return 0.0

        hours = int(parts[0])
        minutes = int(parts[1])
        seconds = int(parts[2])
        frames = int(parts[3])

        # Convert frames to seconds using the actual frame rate from XML
        frame_seconds = frames / frame_rate

        total_seconds = hours * 3600 + minutes * 60 + seconds + frame_seconds

        # Log detailed timing for debugging (only for first few entries)
        if hours == 0 and minutes <= 1:  # Only log first minute for debugging
            logger.debug(f"Timing: {time_str} @ {frame_rate}fps = {total_seconds:.6f}s (frames: {frames})")

        return total_seconds
    except Exception as e:
        logger.debug(f"Failed to parse time '{time_str}': {e}")
        return 0.0


async def _extract_images_from_sup(sup_file: Path) -> list:
    """
    Extract subtitle images and timing from SUP file.
    Returns list of (start_time, end_time, image_data) tuples.

    SUP files contain PGS (Presentation Graphics Stream) data with:
    - PCS: Presentation Composition Segment (timing + composition)
    - WDS: Window Definition Segment (window properties)
    - PDS: Palette Definition Segment (color palette)
    - ODS: Object Definition Segment (actual image data)
    - END: End of Display Set Segment
    """
    try:
        logger.info("📦 Parsing SUP file with proper PGS parser...")

        with open(sup_file, 'rb') as f:
            data = f.read()

        subtitles = []
        offset = 0

        while offset < len(data) - 13:  # Minimum segment size
            # Look for PGS magic bytes "PG"
            if data[offset:offset+2] != b'PG':
                offset += 1
                continue

            try:
                # Parse PGS segment header
                pts = int.from_bytes(data[offset+2:offset+6], 'big')  # Presentation timestamp
                dts = int.from_bytes(data[offset+6:offset+10], 'big')  # Decode timestamp
                segment_type = data[offset+10]
                segment_size = int.from_bytes(data[offset+11:offset+13], 'big')

                # Check if we have enough data
                if offset + 13 + segment_size > len(data):
                    break

                segment_data = data[offset+13:offset+13+segment_size]

                # Convert PTS to seconds (PTS is in 90kHz units)
                start_time = pts / 90000.0

                if segment_type == 0x16:  # PCS - Presentation Composition Segment
                    # This contains timing and composition info
                    if len(segment_data) >= 11:
                        width = int.from_bytes(segment_data[0:2], 'big')
                        height = int.from_bytes(segment_data[2:4], 'big')
                        frame_rate = segment_data[4]
                        composition_number = int.from_bytes(segment_data[5:7], 'big')
                        composition_state = segment_data[7]
                        palette_update_flag = segment_data[8]
                        palette_id = segment_data[9]
                        num_composition_objects = segment_data[10]

                        logger.debug(f"PCS: {width}x{height}, objects: {num_composition_objects}")

                        # Store composition info for later use
                        current_subtitle = {
                            'start_time': start_time,
                            'end_time': start_time + 3.0,  # Default duration, will be updated
                            'width': width,
                            'height': height,
                            'objects': []
                        }

                elif segment_type == 0x17:  # WDS - Window Definition Segment
                    logger.debug("WDS: Window definition")

                elif segment_type == 0x14:  # PDS - Palette Definition Segment
                    logger.debug("PDS: Palette definition")

                elif segment_type == 0x15:  # ODS - Object Definition Segment
                    # This contains the actual image data
                    if len(segment_data) >= 4:
                        object_id = int.from_bytes(segment_data[0:2], 'big')
                        object_version = segment_data[2]
                        sequence_flag = segment_data[3]

                        if sequence_flag & 0x80:  # First in sequence
                            # Extract image dimensions and data
                            if len(segment_data) >= 7:
                                obj_width = int.from_bytes(segment_data[7:9], 'big')
                                obj_height = int.from_bytes(segment_data[9:11], 'big')
                                image_data = segment_data[11:]  # RLE compressed image data

                                logger.debug(f"ODS: Object {object_id}, {obj_width}x{obj_height}")

                                # Create a subtitle entry
                                subtitle_entry = (
                                    start_time,
                                    start_time + 3.0,  # Default 3 second duration
                                    image_data  # Raw RLE data - we'll need to decode this
                                )
                                subtitles.append(subtitle_entry)

                elif segment_type == 0x80:  # END - End of Display Set
                    logger.debug("END: End of display set")

                # Move to next segment
                offset += 13 + segment_size

            except Exception as e:
                logger.debug(f"Error parsing segment at offset {offset}: {e}")
                offset += 1
                continue

        logger.info(f"📦 Successfully parsed {len(subtitles)} subtitle segments from SUP")

        # Convert RLE image data to PNG for Ollama Vision
        processed_subtitles = []
        for i, (start_time, end_time, rle_data) in enumerate(subtitles):
            try:
                # Convert RLE to PNG image
                png_data = await _convert_rle_to_png(rle_data, 1920, 1080)  # Assume 1080p
                if png_data:
                    processed_subtitles.append((start_time, end_time, png_data))
                    logger.debug(f"Converted subtitle {i+1} to PNG")
            except Exception as e:
                logger.debug(f"Failed to convert subtitle {i+1}: {e}")
                continue

        logger.info(f"✅ Successfully processed {len(processed_subtitles)} subtitles for OCR")
        return processed_subtitles

    except Exception as e:
        logger.error(f"❌ Failed to parse SUP file: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return []


async def _convert_rle_to_png(rle_data: bytes, width: int, height: int) -> bytes:
    """
    Convert RLE compressed PGS image data to PNG format.
    PGS uses Run-Length Encoding for compression.
    """
    try:
        from PIL import Image
        import io

        # Decode RLE data to raw pixel data
        pixels = []
        i = 0

        while i < len(rle_data):
            if i >= len(rle_data):
                break

            byte = rle_data[i]
            i += 1

            if byte == 0:  # Escape sequence
                if i >= len(rle_data):
                    break
                next_byte = rle_data[i]
                i += 1

                if next_byte == 0:  # End of line
                    continue
                elif next_byte < 0x40:  # Short run
                    count = next_byte
                    if i < len(rle_data):
                        color = rle_data[i]
                        i += 1
                        pixels.extend([color] * count)
                elif next_byte < 0x80:  # Medium run
                    if i < len(rle_data):
                        count = ((next_byte - 0x40) << 8) + rle_data[i]
                        i += 1
                        if i < len(rle_data):
                            color = rle_data[i]
                            i += 1
                            pixels.extend([color] * count)
                else:  # Long run
                    if i + 1 < len(rle_data):
                        count = ((next_byte - 0x80) << 16) + (rle_data[i] << 8) + rle_data[i+1]
                        i += 2
                        if i < len(rle_data):
                            color = rle_data[i]
                            i += 1
                            pixels.extend([color] * count)
            else:  # Single pixel
                pixels.append(byte)

        # Create image from pixel data
        # For now, create a simple grayscale image
        # In a full implementation, we'd use the palette data
        if len(pixels) < width * height:
            pixels.extend([0] * (width * height - len(pixels)))
        elif len(pixels) > width * height:
            pixels = pixels[:width * height]

        # Convert to PIL Image
        img = Image.new('L', (width, height))
        img.putdata(pixels)

        # Convert to PNG bytes
        png_buffer = io.BytesIO()
        img.save(png_buffer, format='PNG')
        png_data = png_buffer.getvalue()

        logger.debug(f"Converted RLE to PNG: {len(rle_data)} -> {len(png_data)} bytes")
        return png_data

    except Exception as e:
        logger.error(f"❌ Failed to convert RLE to PNG: {e}")
        return b''


async def _ocr_image_with_ollama(image_data: bytes, ollama_model: str, language: str) -> str:
    """
    OCR an image using Ollama Vision model.
    """
    try:
        import base64
        import json
        import aiohttp

        # Convert image data to base64
        image_b64 = base64.b64encode(image_data).decode('utf-8')

        # Prepare Ollama Vision request
        prompt = f"Extract the text from this subtitle image. The text is in {language}. Return only the text content, no descriptions or explanations."

        payload = {
            "model": ollama_model,
            "prompt": prompt,
            "images": [image_b64],
            "stream": False
        }

        # Send request to Ollama
        async with aiohttp.ClientSession() as session:
            async with session.post('http://localhost:11434/api/generate', json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    text = result.get('response', '').strip()
                    return text
                else:
                    logger.error(f"Ollama request failed: {response.status}")
                    return ""

    except Exception as e:
        logger.error(f"❌ Ollama Vision OCR failed: {e}")
        return ""


async def _generate_srt_file(srt_entries: list, output_file: Path) -> bool:
    """
    Generate SRT file from subtitle entries.
    """
    try:
        logger.info(f"📝 Writing {len(srt_entries)} subtitles to SRT file...")

        with open(output_file, 'w', encoding='utf-8') as f:
            for entry in srt_entries:
                # Format timestamps
                start_time = _format_srt_timestamp(entry['start'])
                end_time = _format_srt_timestamp(entry['end'])

                # Write SRT entry
                f.write(f"{entry['index']}\n")
                f.write(f"{start_time} --> {end_time}\n")
                f.write(f"{entry['text']}\n\n")

        logger.info(f"✅ SRT file generated: {output_file}")
        return True

    except Exception as e:
        logger.error(f"❌ Failed to generate SRT file: {e}")
        return False


def _format_srt_timestamp(seconds: float) -> str:
    """
    Format seconds as SRT timestamp (HH:MM:SS,mmm).
    """
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millisecs = int((seconds % 1) * 1000)

    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"


# Legacy function name for compatibility
async def convert_sup_to_srt_basic_pipeline(
    sup_file: Path,
    output_srt: Path,
    settings: dict
) -> bool:
    """
    Legacy function name that now uses Subtitle Edit integration.
    Maintained for compatibility with existing code.
    """
    return await convert_sup_to_srt_with_subtitle_edit(sup_file, output_srt, settings)