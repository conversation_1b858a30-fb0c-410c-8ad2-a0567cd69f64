#!/usr/bin/env python3
"""
Final test for BDSup2Sub extraction folder organization and cleanup
"""

import asyncio
import sys
import logging
from pathlib import Path

# Add paths for imports
sys.path.insert(0, str(Path(__file__).parent / "_internal"))

from _internal.utils.ocr_utils import _convert_sup_with_bdsup2sub_ollama

# Setup basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

async def test_final_organization():
    """Final test for extraction folder organization and cleanup"""
    
    print("🧪 Final Test: BDSup2Sub Extraction Organization & Cleanup")
    print("=" * 65)
    
    # Test SUP file
    sup_file = Path("workspace/3_mkv_cleaned_subtitles_extracted/1080p/Precious (2009)/_Processed_Subtitles/Precious (2009).English.sup")
    
    if not sup_file.exists():
        print(f"❌ Test SUP file not found: {sup_file}")
        return False
    
    print(f"📁 Input SUP file: {sup_file}")
    print(f"📊 File size: {sup_file.stat().st_size} bytes")
    
    # Expected extraction folder location
    expected_extraction_folder = sup_file.parent / "bd2sup_extraction"
    print(f"📂 Expected extraction folder: {expected_extraction_folder}")
    
    # Output SRT file
    output_srt = Path("test_final_output.srt")
    
    print(f"\n🚀 Starting final test with 2 images...")
    print("   This will test:")
    print("   1. ✅ Extraction folder created in correct location")
    print("   2. ✅ Images and XML extracted to that folder")
    print("   3. ✅ OCR processing works correctly")
    print("   4. ✅ Folder cleaned up after completion")
    print("   5. ✅ Original SUP file preserved")
    
    # Verify extraction folder doesn't exist before
    if expected_extraction_folder.exists():
        print(f"⚠️  Extraction folder already exists, removing it first...")
        import shutil
        shutil.rmtree(expected_extraction_folder)
    
    try:
        # Test with 2 images only
        success = await _convert_sup_with_bdsup2sub_ollama(
            sup_file=sup_file,
            output_srt=output_srt,
            ollama_model="qwen2.5vl:latest",
            ocr_language="English",
            max_images=2
        )
        
        # Check results
        folder_cleaned_up = not expected_extraction_folder.exists()
        original_sup_exists = sup_file.exists()
        srt_created = output_srt.exists()
        
        print(f"\n📊 Test Results:")
        print(f"   OCR Success: {'✅' if success else '❌'}")
        print(f"   SRT Created: {'✅' if srt_created else '❌'}")
        print(f"   Folder Cleaned: {'✅' if folder_cleaned_up else '❌'}")
        print(f"   Original SUP Preserved: {'✅' if original_sup_exists else '❌'}")
        
        if srt_created:
            with open(output_srt, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"   SRT Content Preview:")
                lines = content.split('\n')[:8]
                for line in lines:
                    print(f"     {line}")
        
        # Overall success
        overall_success = success and srt_created and folder_cleaned_up and original_sup_exists
        
        if overall_success:
            print(f"\n🎉 All tests passed!")
            print(f"✅ BDSup2Sub extraction folder organization working perfectly")
        else:
            print(f"\n💥 Some tests failed!")
            
        return overall_success
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up test output file
        if output_srt.exists():
            output_srt.unlink()

if __name__ == "__main__":
    print("🎬 Final BDSup2Sub Organization Test")
    print("🔧 Testing complete workflow with proper cleanup")
    
    try:
        success = asyncio.run(test_final_organization())
        
        if success:
            print("\n🎉 Final test completed successfully!")
            print("✅ BDSup2Sub extraction folder organization is production ready!")
            print("🚀 Ready for integration into PlexMovieAutomator workflow")
            sys.exit(0)
        else:
            print("\n💥 Final test failed!")
            print("❌ BDSup2Sub extraction folder organization needs fixes")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⛔ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Critical test error: {e}")
        sys.exit(1)
